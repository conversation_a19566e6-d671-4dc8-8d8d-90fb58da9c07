#!/bin/bash

# Deep Research Agent MVP 安装脚本

echo "🚀 开始安装 Deep Research Agent MVP..."

# 检查Python版本
python_version=$(python3 --version 2>&1 | grep -o '[0-9]\+\.[0-9]\+')
required_version="3.12"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ 错误: 需要Python 3.12+，当前版本: $python_version"
    exit 1
fi

echo "✅ Python版本检查通过: $python_version"

# 安装依赖
echo "📦 安装依赖..."
python3 -m pip install --upgrade pip
python3 -m pip install -e .

if [ $? -ne 0 ]; then
    echo "❌ 依赖安装失败"
    exit 1
fi

echo "✅ 依赖安装完成"

# 创建必要目录
echo "📁 创建必要目录..."
mkdir -p output tests

# 复制环境变量模板
if [ ! -f .env ]; then
    echo "📝 创建环境变量文件..."
    cp .env.example .env
    echo "⚠️  请编辑 .env 文件，添加您的API密钥"
else
    echo "✅ 环境变量文件已存在"
fi

echo ""
echo "🎉 安装完成！"
echo ""
echo "📋 下一步："
echo "1. 编辑 .env 文件，添加API密钥："
echo "   - GEMINI_API_KEY=your_gemini_api_key"
echo "   - TAVILY_API_KEY=your_tavily_api_key (可选)"
echo ""
echo "2. 运行程序："
echo "   python run.py"
echo ""
echo "3. 或直接运行："
echo "   python -m src.deep_research_agent.main"
echo ""
