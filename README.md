# Deep Research Agent MVP

基于CrewAI的智能研究助手，能够自动化完成从需求分析到报告生成的完整研究流程。

## 🎯 项目概述

Deep Research Agent MVP 是一个基于最新CrewAI框架的自动化研究系统，具备以下核心能力：

- **智能研究规划**：自动分析研究主题，生成搜索策略
- **多源信息收集**：集成Tavily API和DuckDuckGo搜索
- **质量控制机制**：智能筛选和去重处理
- **结构化报告生成**：基于模板的Markdown报告输出

## ✨ 核心特性

- 🤖 **三智能体协作**：研究规划师、信息收集师、报告生成师
- 🔍 **多搜索源支持**：Tavily API（主）+ DuckDuckGo（备）
- 📊 **质量评估**：来源可靠性和内容相关性评分
- 📝 **标准化输出**：结构清晰的Markdown研究报告
- ⚡ **快速响应**：5分钟内完成完整研究流程

## 🏗️ 系统架构

```
用户输入 → 研究规划师 → 信息收集师 → 报告生成师 → Markdown报告
```

### 智能体设计

1. **研究规划师**：分析主题，生成搜索关键词和策略
2. **信息收集师**：执行搜索，质量筛选和去重处理  
3. **报告生成师**：整合信息，生成结构化报告

## 🚀 快速开始

### 环境要求

- Python 3.12+
- 网络连接（用于API调用）

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd deep-research-agent
```

2. **安装依赖**
```bash
# 推荐使用uv
uv sync

# 或使用pip
pip install -e .
```

3. **配置环境变量**
```bash
cp .env.example .env
# 编辑.env文件，添加API密钥
```

4. **运行程序**
```bash
python -m src.deep_research_agent.main
```

### API密钥配置

在 `.env` 文件中配置以下API密钥：

```env
GEMINI_API_KEY=your_gemini_api_key_here
TAVILY_API_KEY=your_tavily_api_key_here  # 可选，有助于提升搜索质量
```

#### 获取API密钥

1. **Gemini API**：访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. **Tavily API**：访问 [Tavily](https://tavily.com/) (前1000次免费)

## 📖 使用指南

### 基本使用

1. 启动程序后，输入研究主题
2. 系统自动执行三阶段研究流程：
   - 研究规划：生成搜索策略
   - 信息收集：多源搜索和筛选
   - 报告生成：结构化输出
3. 生成的报告保存在 `output/` 目录

### 示例主题

- "人工智能在医疗领域的应用"
- "区块链技术发展现状"
- "可再生能源市场趋势"
- "远程工作对企业的影响"

### 配置调整

编辑YAML配置文件可以调整：

- `src/deep_research_agent/config/agents.yaml`：智能体配置
- `src/deep_research_agent/config/tasks.yaml`：任务配置

## 📁 项目结构

```
deep-research-agent/
├── src/deep_research_agent/
│   ├── config/
│   │   ├── agents.yaml      # 智能体配置
│   │   └── tasks.yaml       # 任务配置
│   ├── tools/
│   │   └── search_tools.py  # 搜索工具
│   ├── crew.py              # Crew主实现
│   └── main.py              # 程序入口
├── output/                  # 生成的报告
├── tests/                   # 测试代码
├── pyproject.toml           # 项目配置
├── .env.example             # 环境变量模板
└── README.md                # 项目说明
```

## 🧪 测试

运行测试套件：

```bash
pytest tests/
```

## 📊 性能指标

### MVP目标

- ✅ 响应时间: < 5分钟
- ✅ 搜索来源: 10-15个
- ✅ 报告长度: 2000-4000字
- ✅ 系统可用性: > 95%
- ✅ 单次成本: < $0.5

### 质量标准

- 搜索相关性: > 70%
- 来源可靠性: > 0.6
- 内容去重率: > 85%
- 报告完整性: > 90%

## 🔧 故障排除

### 常见问题

1. **API密钥错误**
   - 检查 `.env` 文件中的API密钥是否正确
   - 确认API密钥有效且有足够配额

2. **搜索结果不足**
   - 尝试更具体或更广泛的搜索主题
   - 检查网络连接
   - 确认Tavily API配额

3. **报告生成失败**
   - 检查Gemini API配置和密钥
   - 查看控制台日志输出

## 🛣️ 发展路线图

### 短期优化 (1-2个月)
- [ ] 添加更多搜索源
- [ ] 实现并行搜索
- [ ] 增加专门化报告模板
- [ ] 添加Web界面

### 中期扩展 (3-6个月)
- [ ] 向量相似度去重
- [ ] 动态模板系统
- [ ] 多语言支持
- [ ] 用户反馈机制

### 长期愿景 (6-12个月)
- [ ] 完整多智能体协作
- [ ] 实时数据更新
- [ ] 企业级部署
- [ ] API服务

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有问题或建议，请：

1. 提交 [Issue](../../issues)
2. 查看项目文档
3. 联系开发团队

---

**Deep Research Agent MVP** - 让AI为您的研究工作赋能 🚀
