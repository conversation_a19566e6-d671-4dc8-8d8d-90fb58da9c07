[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "deep-research-agent"
version = "0.1.0"
description = "Deep Research Agent MVP - AI-powered research automation system"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "crewai[tools]>=0.80.0",
    "google-generativeai>=0.8.0",
    "tavily-python>=0.3.0",
    "duckduckgo-search>=6.0.0",
    "pydantic>=2.0.0",
    "python-dotenv>=1.0.0",
    "rich>=13.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.0.0",
]

[tool.black]
line-length = 88
target-version = ['py312']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[project.scripts]
research = "src.deep_research_agent.main:main"
