"""Tavily搜索工具"""
import logging
from typing import List, Optional
from datetime import datetime
import asyncio

try:
    from tavily import TavilyClient
except ImportError:
    TavilyClient = None

from models.schemas import SearchResult, SearchError
from utils.config import config

logger = logging.getLogger(__name__)


class TavilySearchTool:
    """Tavily搜索工具"""
    
    def __init__(self):
        self.api_key = config.get('search.tavily.api_key')
        self.max_results = config.get('search.tavily.max_results_per_query', 3)
        self.timeout = config.get('search.tavily.timeout', 30)
        
        if not self.api_key:
            logger.warning("Tavily API key not found")
            self.client = None
        elif TavilyClient is None:
            logger.warning("Tavily client not available")
            self.client = None
        else:
            try:
                self.client = TavilyClient(api_key=self.api_key)
                logger.info("Tavily client initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize Tavily client: {e}")
                self.client = None
    
    def is_available(self) -> bool:
        """检查Tavily搜索是否可用"""
        return self.client is not None
    
    async def search(self, query: str) -> List[SearchResult]:
        """执行搜索"""
        if not self.is_available():
            raise SearchError("Tavily search not available")
        
        logger.info(f"Tavily搜索: {query}")
        
        try:
            # 使用asyncio.wait_for添加超时控制
            search_task = asyncio.create_task(self._perform_search(query))
            results = await asyncio.wait_for(search_task, timeout=self.timeout)
            
            logger.info(f"Tavily搜索完成，获得 {len(results)} 个结果")
            return results
            
        except asyncio.TimeoutError:
            logger.error(f"Tavily搜索超时: {query}")
            raise SearchError(f"Search timeout for query: {query}")
        except Exception as e:
            logger.error(f"Tavily搜索失败: {query}, 错误: {e}")
            raise SearchError(f"Tavily search failed: {str(e)}")
    
    async def _perform_search(self, query: str) -> List[SearchResult]:
        """执行实际搜索"""
        try:
            # Tavily搜索
            response = self.client.search(
                query=query,
                search_depth="basic",
                max_results=self.max_results,
                include_answer=False,
                include_raw_content=False
            )
            
            results = []
            
            if 'results' in response:
                for item in response['results']:
                    try:
                        result = SearchResult(
                            query=query,
                            title=item.get('title', ''),
                            content=item.get('content', ''),
                            url=item.get('url', ''),
                            source="tavily",
                            timestamp=datetime.now(),
                            relevance_score=self._calculate_relevance_score(query, item),
                            credibility_score=self._calculate_credibility_score(item)
                        )
                        results.append(result)
                    except Exception as e:
                        logger.warning(f"Failed to parse Tavily result: {e}")
                        continue
            
            return results
            
        except Exception as e:
            logger.error(f"Tavily API error: {e}")
            raise SearchError(f"Tavily API error: {str(e)}")
    
    def _calculate_relevance_score(self, query: str, item: dict) -> float:
        """计算相关性评分"""
        try:
            title = item.get('title', '').lower()
            content = item.get('content', '').lower()
            query_lower = query.lower()
            
            # 简单的关键词匹配评分
            query_words = query_lower.split()
            title_matches = sum(1 for word in query_words if word in title)
            content_matches = sum(1 for word in query_words if word in content)
            
            # 计算匹配率
            title_score = title_matches / len(query_words) if query_words else 0
            content_score = content_matches / len(query_words) if query_words else 0
            
            # 标题权重更高
            relevance_score = (title_score * 0.7 + content_score * 0.3)
            
            return min(relevance_score, 1.0)
            
        except Exception:
            return 0.5  # 默认中等相关性
    
    def _calculate_credibility_score(self, item: dict) -> float:
        """计算可信度评分"""
        try:
            url = item.get('url', '').lower()
            title = item.get('title', '').lower()
            content = item.get('content', '')
            
            base_score = 0.5
            
            # 基于域名的可信度评分
            if any(domain in url for domain in ['.gov', '.edu', '.org']):
                base_score += 0.3
            elif any(domain in url for domain in ['wikipedia', 'reuters', 'bbc', 'cnn']):
                base_score += 0.2
            elif any(domain in url for domain in ['medium', 'forbes', 'techcrunch']):
                base_score += 0.1
            
            # 基于内容质量的评分
            if len(content) > 500:  # 内容充实
                base_score += 0.1
            if len(content) > 1000:  # 内容详细
                base_score += 0.1
            
            # 检查是否包含垃圾内容指标
            if content.count('http') > 5:  # 过多链接
                base_score -= 0.1
            if any(spam_word in title for spam_word in ['click', 'buy', 'sale', 'discount']):
                base_score -= 0.2
            
            return max(0.1, min(base_score, 1.0))
            
        except Exception:
            return 0.5  # 默认中等可信度


# 全局Tavily搜索实例
tavily_search = TavilySearchTool()
