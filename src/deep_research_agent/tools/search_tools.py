"""搜索工具模块"""
import os
import logging
from typing import List, Dict, Any, Optional
from crewai_tools import BaseTool
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class SearchResult(BaseModel):
    """搜索结果数据模型"""
    title: str = Field(..., description="结果标题")
    content: str = Field(..., description="内容摘要")
    url: str = Field(..., description="来源URL")
    source: str = Field(..., description="搜索源")


class TavilySearchTool(BaseTool):
    """Tavily搜索工具"""
    name: str = "tavily_search"
    description: str = "使用Tavily API进行网络搜索，获取高质量的搜索结果"
    
    def _run(self, query: str, max_results: int = 5) -> List[Dict[str, Any]]:
        """执行Tavily搜索"""
        try:
            # 检查API密钥
            api_key = os.getenv("TAVILY_API_KEY")
            if not api_key:
                logger.warning("Tavily API key not found")
                return []
            
            # 导入Tavily客户端
            try:
                from tavily import TavilyClient
            except ImportError:
                logger.error("Tavily client not installed")
                return []
            
            # 执行搜索
            client = TavilyClient(api_key=api_key)
            response = client.search(
                query=query,
                search_depth="basic",
                max_results=max_results,
                include_answer=False,
                include_raw_content=False
            )
            
            results = []
            if 'results' in response:
                for item in response['results']:
                    result = {
                        "title": item.get('title', ''),
                        "content": item.get('content', ''),
                        "url": item.get('url', ''),
                        "source": "tavily"
                    }
                    results.append(result)
            
            logger.info(f"Tavily搜索完成: {query}, 获得 {len(results)} 个结果")
            return results
            
        except Exception as e:
            logger.error(f"Tavily搜索失败: {query}, 错误: {e}")
            return []


class DuckDuckGoSearchTool(BaseTool):
    """DuckDuckGo搜索工具"""
    name: str = "duckduckgo_search"
    description: str = "使用DuckDuckGo进行网络搜索，作为备用搜索源"
    
    def _run(self, query: str, max_results: int = 3) -> List[Dict[str, Any]]:
        """执行DuckDuckGo搜索"""
        try:
            # 导入DuckDuckGo搜索
            try:
                from duckduckgo_search import DDGS
            except ImportError:
                logger.error("DuckDuckGo search not installed")
                return []
            
            results = []
            
            with DDGS() as ddgs:
                search_results = list(ddgs.text(
                    keywords=query,
                    max_results=max_results,
                    safesearch='moderate',
                    timelimit='y'  # 限制为一年内的结果
                ))
                
                for item in search_results:
                    result = {
                        "title": item.get('title', ''),
                        "content": item.get('body', ''),
                        "url": item.get('href', ''),
                        "source": "duckduckgo"
                    }
                    results.append(result)
            
            logger.info(f"DuckDuckGo搜索完成: {query}, 获得 {len(results)} 个结果")
            return results
            
        except Exception as e:
            logger.error(f"DuckDuckGo搜索失败: {query}, 错误: {e}")
            return []


class ResearchSearchTool(BaseTool):
    """综合研究搜索工具"""
    name: str = "research_search"
    description: str = "综合使用多个搜索源进行研究，自动处理搜索失败和结果去重"
    
    def __init__(self):
        super().__init__()
        self.tavily_tool = TavilySearchTool()
        self.ddg_tool = DuckDuckGoSearchTool()
    
    def _run(self, keywords: str, max_results_per_source: int = 3) -> List[Dict[str, Any]]:
        """执行综合搜索"""
        if isinstance(keywords, str):
            keyword_list = [kw.strip() for kw in keywords.split(',')]
        else:
            keyword_list = keywords if isinstance(keywords, list) else [str(keywords)]
        
        all_results = []
        
        for keyword in keyword_list:
            if not keyword.strip():
                continue
                
            logger.info(f"搜索关键词: {keyword}")
            
            # 首先尝试Tavily搜索
            tavily_results = self.tavily_tool._run(keyword, max_results_per_source)
            if tavily_results:
                all_results.extend(tavily_results)
            else:
                # Tavily失败时使用DuckDuckGo
                ddg_results = self.ddg_tool._run(keyword, max_results_per_source)
                all_results.extend(ddg_results)
        
        # 简单去重（基于URL）
        seen_urls = set()
        unique_results = []
        
        for result in all_results:
            url = result.get('url', '')
            if url and url not in seen_urls:
                seen_urls.add(url)
                unique_results.append(result)
        
        logger.info(f"综合搜索完成，去重后获得 {len(unique_results)} 个结果")
        return unique_results


# 创建工具实例
tavily_search_tool = TavilySearchTool()
duckduckgo_search_tool = DuckDuckGoSearchTool()
research_search_tool = ResearchSearchTool()
