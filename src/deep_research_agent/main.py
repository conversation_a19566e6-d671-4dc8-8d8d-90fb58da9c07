#!/usr/bin/env python3
"""Deep Research Agent MVP 主程序入口"""
import os
import sys
import asyncio
import logging
from pathlib import Path
from typing import Optional

from dotenv import load_dotenv
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.markdown import Markdown

from .crew import create_crew

# 加载环境变量
load_dotenv()

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)
console = Console()


def check_environment() -> bool:
    """检查环境配置"""
    required_vars = {
        'GEMINI_API_KEY': 'Gemini API密钥',
    }
    
    missing_vars = []
    for var, desc in required_vars.items():
        if not os.getenv(var):
            missing_vars.append(f"{var} ({desc})")
    
    if missing_vars:
        console.print(Panel(
            f"[red]缺少必要的环境变量:[/red]\n" + 
            "\n".join(f"- {var}" for var in missing_vars) +
            f"\n\n请在 .env 文件中配置这些变量",
            title="环境配置错误",
            border_style="red"
        ))
        return False
    
    return True


def display_welcome():
    """显示欢迎信息"""
    console.print(Panel(
        "[bold blue]Deep Research Agent MVP[/bold blue]\n"
        "基于CrewAI的智能研究助手\n\n"
        "功能特性:\n"
        "• 自动化研究规划\n"
        "• 多源信息收集\n"
        "• 智能报告生成\n"
        "• 质量控制机制",
        title="欢迎使用",
        border_style="blue"
    ))


def get_research_topic() -> Optional[str]:
    """获取研究主题"""
    console.print("\n[cyan]请输入您要研究的主题:[/cyan]")
    console.print("[dim]示例: 人工智能在医疗领域的应用、区块链技术发展现状、可再生能源市场趋势[/dim]")
    
    topic = console.input("\n[bold cyan]研究主题: [/bold cyan]").strip()
    
    if not topic:
        console.print("[yellow]请输入有效的研究主题[/yellow]")
        return None
    
    return topic


def run_research(topic: str) -> bool:
    """执行研究任务"""
    try:
        console.print(Panel(
            f"[bold green]开始研究: {topic}[/bold green]",
            title="研究任务",
            border_style="green"
        ))
        
        # 创建Crew
        crew_instance = create_crew()
        
        # 准备输入
        inputs = {
            'topic': topic
        }
        
        # 执行研究
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("正在执行研究任务...", total=None)
            
            try:
                result = crew_instance.crew().kickoff(inputs=inputs)
                progress.update(task, description="✓ 研究任务完成")
                
                # 显示结果
                if result:
                    console.print(Panel(
                        "[bold green]研究完成![/bold green]\n"
                        f"报告已生成并保存到 output/ 目录",
                        title="任务完成",
                        border_style="green"
                    ))
                    
                    # 询问是否显示报告
                    show_report = console.input("\n[cyan]是否显示生成的报告? (y/n): [/cyan]").lower()
                    if show_report in ['y', 'yes', '是']:
                        if isinstance(result, str):
                            console.print("\n" + "="*80)
                            console.print(Markdown(result))
                            console.print("="*80 + "\n")
                        else:
                            console.print(f"[yellow]结果类型: {type(result)}[/yellow]")
                            console.print(str(result))
                
                return True
                
            except Exception as e:
                progress.update(task, description="✗ 研究任务失败")
                logger.error(f"研究任务执行失败: {e}")
                console.print(f"[red]研究失败: {e}[/red]")
                return False
    
    except Exception as e:
        logger.error(f"系统错误: {e}")
        console.print(f"[red]系统错误: {e}[/red]")
        return False


def main():
    """主函数"""
    try:
        # 显示欢迎信息
        display_welcome()
        
        # 检查环境配置
        if not check_environment():
            console.print("\n[yellow]请配置环境变量后重试[/yellow]")
            return
        
        console.print("[green]✓ 环境配置检查通过[/green]")
        
        # 交互式研究
        while True:
            try:
                console.print("\n" + "-"*60)
                
                # 获取研究主题
                topic = get_research_topic()
                if not topic:
                    continue
                
                # 确认研究
                confirm = console.input(f"\n[cyan]确认研究主题「{topic}」? (y/n): [/cyan]").lower()
                if confirm not in ['y', 'yes', '是']:
                    continue
                
                # 执行研究
                success = run_research(topic)
                
                if success:
                    # 询问是否继续
                    continue_research = console.input("\n[cyan]是否进行新的研究? (y/n): [/cyan]").lower()
                    if continue_research not in ['y', 'yes', '是']:
                        break
                else:
                    # 询问是否重试
                    retry = console.input("\n[cyan]是否重试? (y/n): [/cyan]").lower()
                    if retry not in ['y', 'yes', '是']:
                        break
            
            except KeyboardInterrupt:
                console.print("\n[yellow]操作被用户中断[/yellow]")
                break
            except Exception as e:
                logger.error(f"主循环错误: {e}")
                console.print(f"[red]发生错误: {e}[/red]")
                break
        
        console.print("\n[blue]感谢使用 Deep Research Agent![/blue]")
    
    except KeyboardInterrupt:
        console.print("\n[yellow]程序被用户中断[/yellow]")
    except Exception as e:
        logger.error(f"程序错误: {e}")
        console.print(f"[red]程序错误: {e}[/red]")


if __name__ == "__main__":
    main()
