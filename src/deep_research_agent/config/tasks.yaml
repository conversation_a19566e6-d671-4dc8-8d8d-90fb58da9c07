# Deep Research Agent MVP - Tasks Configuration

research_planning_task:
  description: >
    分析研究主题「{topic}」，制定详细的搜索策略。
    
    请按照以下JSON格式输出：
    {{
        "research_topic": "主研究主题",
        "keywords": ["关键词1", "关键词2", "关键词3", "关键词4", "关键词5"],
        "search_strategy": "broad|focused|comparative",
        "estimated_sources": 15
    }}
    
    要求：
    1. 生成5-8个相关的搜索关键词
    2. 关键词应该涵盖主题的不同方面
    3. 选择合适的搜索策略：
       - broad: 广泛搜索，适用于新兴或复杂主题
       - focused: 聚焦搜索，适用于具体明确的主题
       - comparative: 对比搜索，适用于需要比较分析的主题
    4. 预估需要的数据源数量
  expected_output: >
    JSON格式的研究计划，包含研究主题、搜索关键词、搜索策略和预估来源数量
  agent: research_planner

information_collection_task:
  description: >
    基于研究计划，执行信息收集任务：
    
    1. 使用提供的关键词进行搜索
    2. 收集相关的信息和数据
    3. 评估信息来源的可靠性和相关性
    4. 过滤低质量和重复内容
    5. 确保收集到足够数量的高质量来源
    
    质量标准：
    - 来源可靠性评分 > 0.4
    - 内容长度 > 200字符
    - 相关性评分 > 0.3
    - 最少需要5个有效来源
  expected_output: >
    结构化的搜索结果列表，包含标题、内容摘要、来源URL、可靠性评分等信息，
    以及质量评估报告
  agent: information_collector
  context:
    - research_planning_task

report_generation_task:
  description: >
    基于收集的信息，生成关于「{topic}」的专业研究报告：
    
    1. 分析和整合所有搜索结果
    2. 按照标准模板构建报告结构
    3. 确保内容逻辑清晰，语言流畅
    4. 添加完整的引用和来源标注
    5. 包含质量元数据和统计信息
    
    报告结构要求：
    - 执行摘要
    - 背景介绍
    - 现状分析
    - 关键发现
    - 深度分析
    - 结论与建议
    - 参考资料
    - 报告元数据
  expected_output: >
    完整的Markdown格式研究报告，包含所有必要章节、引用和元数据。
    报告应该结构清晰、内容详实、引用规范。
  agent: report_generator
  context:
    - research_planning_task
    - information_collection_task
  output_file: 'output/research_report.md'
