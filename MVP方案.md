# Deep Research Agent MVP 技术方案

## 1. MVP概述

### 1.1 项目目标
构建一个最小可行产品(MVP)版本的AI研究助手，验证"自动化研究报告生成"的核心价值假设，为后续功能扩展奠定基础。

### 1.2 核心价值主张
- **自动化研究**：用户输入研究主题，系统自动完成信息收集和报告生成
- **结构化输出**：生成格式规范、引用完整的Markdown研究报告
- **快速交付**：5分钟内完成完整研究流程
- **成本可控**：单次研究成本控制在$0.5以内

### 1.3 MVP功能边界

#### ✅ 包含功能
- 自然语言研究主题输入
- 基于关键词的智能搜索
- 信息去重和质量筛选
- 结构化报告生成
- 完整引用列表

#### ❌ 暂不包含
- 复杂的并行处理
- 多层缓存系统
- 动态模板适配
- 向量相似度分析
- 多语言支持

## 2. 系统架构

### 2.1 简化架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户输入      │ -> │  研究规划师     │ -> │  信息收集师     │
│ (研究主题)      │    │ (生成搜索计划)  │    │ (执行搜索任务)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Markdown报告   │ <- │  报告生成师     │ <- │   搜索结果      │
│   (最终输出)    │    │ (整合生成报告)  │    │  (结构化数据)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 技术栈选择

```yaml
核心框架: CrewAI 0.28+
LLM模型: Gemini-1.5 Flash
搜索API: Tavily API (主要) + DuckDuckGo (备用)
缓存: 内存缓存 (Python dict)
配置: YAML + 环境变量
依赖管理: uv
开发语言: Python 3.9+
```

## 3. 智能体设计

### 3.1 研究规划师 (Research Planner)

**角色定义**：
```yaml
role: "研究任务规划专家"
goal: "分析用户需求并制定高效的搜索策略"
backstory: "你是一位经验丰富的研究方法专家，擅长将复杂问题分解为可执行的搜索任务"
```

**核心职责**：
- 解析用户输入的研究主题
- 生成5-8个核心搜索关键词
- 确定搜索优先级和策略
- 预估研究复杂度

**输出格式**：
```json
{
    "research_topic": "主研究主题",
    "keywords": ["关键词1", "关键词2", "关键词3"],
    "search_strategy": "broad|focused|comparative",
    "estimated_sources": 15
}
```

### 3.2 信息收集师 (Information Collector)

**角色定义**：
```yaml
role: "信息搜索和筛选专家"
goal: "高效收集相关信息并进行初步质量评估"
backstory: "你是一位信息检索专家，擅长从海量信息中筛选出高质量、相关性强的内容"
```

**核心职责**：
- 执行搜索任务（串行处理）
- 评估搜索结果的相关性和可靠性
- 执行基础去重处理
- 结构化存储搜索结果

**质量评估标准**：
```python
def calculate_source_score(source_url, content):
    base_score = 0.5
    
    # 来源可靠性加分
    if any(domain in source_url for domain in ['gov', 'edu', 'org']):
        base_score += 0.3
    elif any(domain in source_url for domain in ['wikipedia', 'reuters', 'bbc']):
        base_score += 0.2
    
    # 内容质量加分
    if len(content) > 500:  # 内容充实
        base_score += 0.1
    if content.count('http') < 5:  # 非垃圾链接
        base_score += 0.1
        
    return min(base_score, 1.0)
```

### 3.3 报告生成师 (Report Generator)

**角色定义**：
```yaml
role: "专业研究报告撰写专家"
goal: "将搜索结果整合为结构清晰、内容详实的研究报告"
backstory: "你是一位资深的研究报告撰写专家，擅长将分散信息整合为逻辑清晰的专业报告"
```

**核心职责**：
- 分析和整合搜索结果
- 按固定模板生成报告结构
- 添加引用和来源标注
- 生成质量元数据

## 4. 核心功能实现

### 4.1 搜索机制

**搜索流程**：
```python
async def search_information(keywords: List[str]) -> List[SearchResult]:
    results = []
    cache = {}  # 简单内存缓存
    
    for keyword in keywords:
        if keyword in cache:
            results.extend(cache[keyword])
            continue
            
        try:
            # 主搜索源
            tavily_results = await search_tavily(keyword, max_results=3)
            results.extend(tavily_results)
            cache[keyword] = tavily_results
            
        except Exception:
            # 备用搜索源
            ddg_results = await search_duckduckgo(keyword, max_results=2)
            results.extend(ddg_results)
            cache[keyword] = ddg_results
    
    return deduplicate_results(results)
```

**去重策略**：
- URL完全匹配去重
- 标题相似度去重（简单字符串匹配）
- 内容长度过滤（< 200字符的结果丢弃）

### 4.2 报告模板

**通用研究报告模板**：
```markdown
# {研究主题} - 研究报告

## 执行摘要
{核心发现和结论的简要概述}

## 1. 背景介绍
{研究主题的背景和重要性}

## 2. 现状分析
{当前状况的详细分析}

## 3. 关键发现
{主要研究发现和数据}

## 4. 深度分析
{对关键问题的深入探讨}

## 5. 结论与建议
{总结性结论和实用建议}

## 参考资料
{按编号列出的完整引用列表}

---
**报告元数据**
- 生成时间: {timestamp}
- 数据来源: {source_count} 个
- 平均来源评分: {avg_score}
- 研究用时: {duration}
```

### 4.3 错误处理

**容错机制**：
```python
class ResearchError(Exception):
    pass

def handle_search_failure(keyword: str, error: Exception):
    logger.warning(f"搜索失败: {keyword}, 错误: {error}")
    # 继续其他关键词搜索，不中断整个流程
    
def handle_api_timeout():
    # 30秒超时后切换到备用搜索源
    return "切换到DuckDuckGo搜索"
    
def ensure_minimum_sources(results: List[SearchResult]):
    if len(results) < 5:
        raise ResearchError("搜索结果不足，无法生成可靠报告")
```

## 5. 项目结构

```
deep-research-agent-mvp/
├── pyproject.toml              # uv项目配置
├── .env.example               # 环境变量模板
├── config.yaml               # 应用配置
├── README.md                 # 项目说明
├── src/
│   ├── __init__.py
│   ├── main.py              # 主程序入口
│   ├── agents/              # 智能体定义
│   │   ├── __init__.py
│   │   ├── planner.py       # 研究规划师
│   │   ├── collector.py     # 信息收集师
│   │   └── generator.py     # 报告生成师
│   ├── tools/               # 搜索工具
│   │   ├── __init__.py
│   │   ├── tavily_search.py
│   │   └── ddg_search.py
│   ├── utils/               # 工具函数
│   │   ├── __init__.py
│   │   ├── cache.py         # 缓存管理
│   │   ├── dedup.py         # 去重逻辑
│   │   └── template.py      # 模板处理
│   └── models/              # 数据模型
│       ├── __init__.py
│       └── schemas.py       # 数据结构定义
├── data/
│   ├── cache/              # 缓存文件
│   ├── reports/            # 生成的报告
│   └── logs/               # 日志文件
└── tests/                  # 测试代码
    ├── __init__.py
    ├── test_agents.py
    ├── test_tools.py
    └── test_integration.py
```

## 6. 配置管理

**config.yaml**：
```yaml
# LLM配置
llm:
  provider: "gemini"
  model: "gemini-1.5-flash"
  api_key: "${GEMINI_API_KEY}"
  max_tokens: 4096
  temperature: 0.1

# 搜索配置
search:
  tavily:
    api_key: "${TAVILY_API_KEY}"
    max_results_per_query: 3
    timeout: 30
  duckduckgo:
    max_results_per_query: 2
    timeout: 20

# 应用配置
app:
  max_keywords: 8
  min_sources: 5
  max_report_length: 5000
  cache_ttl: 3600  # 1小时缓存

# 质量控制
quality:
  min_source_score: 0.4
  min_content_length: 200
  max_duplicate_ratio: 0.3
```

## 7. 开发计划

### 第一阶段（2周）- 核心功能
**目标**：实现基础的端到端研究流程

**任务清单**：
- [ ] 项目环境搭建（uv + Python 3.9+）
- [ ] CrewAI框架集成和配置
- [ ] 3个核心智能体实现
- [ ] Tavily API集成和测试
- [ ] 基础报告模板实现
- [ ] 命令行界面开发

**验收标准**：
- 能够接受用户输入并生成基础报告
- 搜索功能正常工作
- 报告格式符合模板要求

### 第二阶段（1周）- 优化完善
**目标**：提升系统稳定性和用户体验

**任务清单**：
- [ ] 内存缓存机制实现
- [ ] 错误处理和重试逻辑
- [ ] DuckDuckGo备用搜索集成
- [ ] 基础质量评估实现
- [ ] 日志和监控添加

**验收标准**：
- 系统稳定性达到90%
- 搜索失败时能够自动降级
- 生成报告质量稳定

### 第三阶段（1周）- 测试部署
**目标**：完善测试和部署准备

**任务清单**：
- [ ] 单元测试编写（覆盖率>80%）
- [ ] 集成测试实现
- [ ] 性能测试和优化
- [ ] 文档完善
- [ ] 部署脚本准备

**验收标准**：
- 所有测试通过
- 性能指标达标
- 文档完整可用

## 8. 成功指标

### 8.1 功能指标
- ✅ 支持任意研究主题输入
- ✅ 自动搜索10-15个相关来源
- ✅ 生成3000-5000字结构化报告
- ✅ 包含完整引用列表（APA格式）
- ✅ 整个流程在5分钟内完成

### 8.2 质量指标
- ✅ 搜索结果相关性 > 70%
- ✅ 信息来源可靠性平均分 > 0.6
- ✅ 报告结构完整性 > 90%
- ✅ 内容去重率 > 85%

### 8.3 技术指标
- ✅ 系统可用性 > 95%
- ✅ 平均响应时间 < 3分钟
- ✅ API调用成功率 > 90%
- ✅ 内存使用 < 512MB

### 8.4 成本指标
- ✅ 单次研究成本 < $0.5
- ✅ 月度运营成本 < $50
- ✅ API调用效率 > 80%

## 9. 风险控制

### 9.1 技术风险
**API限制风险**：
- 风险：搜索API配额耗尽
- 缓解：实现多搜索源切换，设置调用限制

**质量控制风险**：
- 风险：生成报告质量不稳定
- 缓解：实现最小来源数量检查，质量评分过滤

### 9.2 成本风险
**API成本超支**：
- 风险：LLM和搜索API调用过多
- 缓解：设置每日调用上限，实现缓存机制

### 9.3 用户体验风险
**响应时间过长**：
- 风险：用户等待时间超过预期
- 缓解：设置超时机制，提供进度反馈

## 10. 后续扩展路径

### 10.1 短期扩展（MVP后1-2个月）
- 添加更多搜索源（Bing、Google Scholar）
- 实现简单的并行搜索
- 增加2-3个专门化报告模板
- 添加Web界面

### 10.2 中期扩展（3-6个月）
- 实现向量相似度去重
- 添加动态模板系统
- 支持多语言研究
- 实现用户反馈机制

### 10.3 长期愿景（6-12个月）
- 完整的多智能体协作系统
- 实时数据更新和监控
- 企业级部署和API服务
- 协作研究功能

---

**MVP方案总结**：本MVP方案专注于验证核心价值假设，通过简化架构和功能范围，确保在4周内交付可用的产品原型，为后续迭代奠定坚实基础。
